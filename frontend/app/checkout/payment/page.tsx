'use client';

import ESIMReadyScreen from "@/app/components/ESIMReadyScreen";
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useStripe, Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export default function PaymentPageWrapper() {
    return (
        <Elements stripe={stripePromise}>
            <SuccessHandler />
        </Elements>
    );
}

function SuccessHandler() {
    const router = useRouter();
    const stripe = useStripe();

    const [status, setStatus] = useState<'loading' | 'success' | 'failed'>('loading');

    useEffect(() => {
        console.log('Payment kontrolü başlatıldı');
        const search = window.location.search;
        const params = new URLSearchParams(search);
        const clientSecret = params.get('payment_intent_client_secret');
        console.log('clientSecret:', clientSecret);

        if (!stripe || !clientSecret) {
            return;
        }

        const checkPaymentIntent = async () => {
            try {
                const { paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);
                console.log('PaymentIntent:', paymentIntent);
                if (paymentIntent?.status === 'succeeded') {
                    setStatus('success');
                } else {
                    setStatus('failed');
                }
            } catch (error) {
                console.error('PaymentIntent alınırken hata:', error);
                setStatus('failed');
            }
        };

        checkPaymentIntent();
    }, [stripe]);

    useEffect(() => {
        console.log('Status değişti:', status);
        if (status === 'failed') {
            console.log('Ödeme başarısız, /checkout?error=payment_failed sayfasına yönlendiriliyor');
            router.push('/checkout?error=payment_failed');
        }
    }, [status]);

    if (status === 'loading') {
        return <p>Kontrol ediliyor...</p>;
    }

    if (status === 'success') {
        return (
            <div className="fixed inset-0 bg-white z-50">
                <ESIMReadyScreen />
            </div>
        );
    }

    console.log('Hiçbir durum sağlanmadı, null dönülüyor');
    return null;
}
