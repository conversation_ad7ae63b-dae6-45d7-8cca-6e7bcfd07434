'use client';

import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Elements, useStripe, useElements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { PaymentElement } from '@stripe/react-stripe-js';
import { useTranslation } from 'react-i18next';
import type { StripeElementsOptions } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export interface StripePaymentElementRef {
    confirmStripePayment: () => Promise<{ success: boolean; error?: any }>;
}

interface StripePaymentElementProps {
    clientSecret: string | null;
    onPaymentSuccess: () => void;
    onPaymentError: (error: any) => void;
}

const PaymentForm = forwardRef<StripePaymentElementRef, Omit<StripePaymentElementProps, 'clientSecret'>>(
    ({ onPaymentSuccess, onPaymentError }, ref) => {
        const stripe = useStripe();
        const elements = useElements();
        const { t } = useTranslation();
        const [isProcessing, setIsProcessing] = useState(false);
        const [errorMessage, setErrorMessage] = useState<string | null>(null);

        useImperativeHandle(ref, () => ({
            confirmStripePayment: async () => {
                if (!stripe || !elements) {
                    setErrorMessage("Stripe.js has not yet loaded.");
                    onPaymentError("Stripe.js not loaded");
                    return { success: false, error: "Stripe.js not loaded" };
                }

                setIsProcessing(true);
                setErrorMessage(null);

                const { error, paymentIntent } = await stripe.confirmPayment({
                    elements,
                    confirmParams: {
                        return_url: `${window.location.origin}/checkout/payment`,
                    },
                    redirect: 'if_required'
                });

                if (error) {
                    if (error.type === "card_error" || error.type === "validation_error") {
                        setErrorMessage(error.message || "An unexpected error occurred.");
                    } else {
                        setErrorMessage("An unexpected error occurred.");
                    }
                    onPaymentError(error);
                    setIsProcessing(false);
                    return { success: false, error };
                } else if (paymentIntent && paymentIntent.status === 'succeeded') {
                    onPaymentSuccess();
                    setIsProcessing(false);
                    return { success: true };
                } else {
                    setErrorMessage("Payment not succeeded. Status: " + paymentIntent?.status);
                    onPaymentError(paymentIntent);
                    setIsProcessing(false);
                    return { success: false, error: paymentIntent };
                }
            },
        }));

        return (
            <form id="stripe-payment-element">
                <PaymentElement />
                {errorMessage && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-2xl">
                        {errorMessage}
                    </div>
                )}
                {isProcessing && (
                    <div className="mt-4 text-center text-gray-600">
                        {t('checkout.processing.wait')}
                    </div>
                )}
            </form>
        );
    }
);

const StripePaymentElement = forwardRef<StripePaymentElementRef, StripePaymentElementProps>(({
    clientSecret,
    onPaymentSuccess,
    onPaymentError,
}, ref) => {
    const { t } = useTranslation();

    if (!clientSecret) {
        return null;
    }

    const options: StripeElementsOptions = {
        clientSecret,
        appearance: {
            theme: 'stripe',
            variables: {
                colorPrimary: '#000000',
                colorText: '#000000',
                borderRadius: '16px',
            },
        },
    };

    return (
        <Elements stripe={stripePromise} options={options}>
            <PaymentForm
                ref={ref}
                onPaymentSuccess={onPaymentSuccess}
                onPaymentError={onPaymentError}
            />
        </Elements>
    );
});

StripePaymentElement.displayName = 'StripePaymentElement';

export default StripePaymentElement;
